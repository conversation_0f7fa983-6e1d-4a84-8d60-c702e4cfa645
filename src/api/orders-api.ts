import type {
  DocumentSnapshot,
  QueryDocumentSnapshot,
} from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  AppCloudFunctions,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
} from '@/constants/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

import { getCurrentUserId } from './auth-api';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export type OrderType = 'buyers' | 'sellers' | 'secondary-market';

interface OrderQueryConfig {
  status: string;
  additionalWhere?: Array<{
    field: string;
    operator: '!=' | '>' | '<' | '==' | '>=' | '<=';
    value: unknown;
  }>;
  priceField: 'amount' | 'secondaryMarketPrice';
  filterLogic: (order: OrderEntity, filters: OrderFilters) => boolean;
}

const ORDER_CONFIGS: Record<OrderType, OrderQueryConfig> = {
  buyers: {
    status: 'active',
    additionalWhere: [
      { field: 'sellerId', operator: '!=', value: null },
      { field: 'secondaryMarketPrice', operator: '==', value: null },
    ],
    priceField: 'amount',
    filterLogic: (order, filters) =>
      !order.buyerId &&
      (!filters.currentUserId || order.sellerId !== filters.currentUserId),
  },
  sellers: {
    status: 'active',
    additionalWhere: [
      { field: 'buyerId', operator: '!=', value: null },
      { field: 'secondaryMarketPrice', operator: '==', value: null },
    ],
    priceField: 'amount',
    filterLogic: (order, filters) =>
      !order.sellerId &&
      (!filters.currentUserId || order.buyerId !== filters.currentUserId),
  },
  'secondary-market': {
    status: 'paid',
    additionalWhere: [
      { field: 'secondaryMarketPrice', operator: '>', value: 0 },
    ],
    priceField: 'secondaryMarketPrice',
    filterLogic: (order, filters) =>
      !filters.currentUserId ||
      order.buyerId === filters.currentUserId ||
      order.sellerId === filters.currentUserId,
  },
};

const buildOrderQuery = (
  config: OrderQueryConfig,
  filters: OrderFilters,
  pageSize: number,
) => {
  let q = query(
    collection(firestore, ORDERS_COLLECTION_NAME),
    where('status', '==', config.status),
  );

  if (config.additionalWhere) {
    for (const condition of config.additionalWhere) {
      q = query(q, where(condition.field, condition.operator, condition.value));
    }
  }

  if (filters.collectionId) {
    q = query(q, where('collectionId', '==', filters.collectionId));
  }

  const sortField = filters.sortBy?.includes('price')
    ? config.priceField
    : 'createdAt';
  const sortDirection = filters.sortBy?.includes('asc') ? 'asc' : 'desc';
  q = query(q, orderBy(sortField, sortDirection));

  if (filters.lastDoc) {
    q = query(q, startAfter(filters.lastDoc));
  }

  return query(q, limit(pageSize + 1));
};

const processOrderDocuments = (
  docs: QueryDocumentSnapshot[],
  config: OrderQueryConfig,
  filters: OrderFilters,
  pageSize: number,
) => {
  const orders: OrderEntity[] = [];
  let lastDoc = null;
  let hasMore = false;
  let processedCount = 0;

  for (const doc of docs) {
    if (processedCount >= pageSize) {
      hasMore = true;
      break;
    }

    const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

    if (config.filterLogic(orderData, filters)) {
      orders.push(orderData);
      lastDoc = doc;
      processedCount++;
    }
  }

  return { orders, lastDoc, hasMore };
};

const applyPriceFilters = (
  orders: OrderEntity[],
  config: OrderQueryConfig,
  filters: OrderFilters,
) => {
  let filteredOrders = orders;

  if (filters.minPrice !== undefined) {
    filteredOrders = filteredOrders.filter((order) => {
      const price =
        config.priceField === 'secondaryMarketPrice'
          ? (order.secondaryMarketPrice ?? 0)
          : order.price;
      return price >= filters.minPrice!;
    });
  }

  if (filters.maxPrice !== undefined) {
    filteredOrders = filteredOrders.filter((order) => {
      const price =
        config.priceField === 'secondaryMarketPrice'
          ? (order.secondaryMarketPrice ?? 0)
          : order.price;
      return price <= filters.maxPrice!;
    });
  }

  return filteredOrders;
};

export const getOrders = async (
  orderType: OrderType,
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const pageSize = filters.limit ?? 20;
    const config = ORDER_CONFIGS[orderType];

    const q = buildOrderQuery(config, filters, pageSize);
    const snapshot = await getDocs(q);

    const {
      orders: processedOrders,
      lastDoc,
      hasMore,
    } = processOrderDocuments(snapshot.docs, config, filters, pageSize);

    const orders = applyPriceFilters(processedOrders, config, filters);

    return { orders, lastDoc, hasMore };
  } catch (error) {
    console.error(`Error fetching ${orderType} orders:`, error);
    throw error;
  }
};

export const getOrdersForBuyers = (filters: OrderFilters = {}) =>
  getOrders('buyers', filters);

export const getOrdersForSellers = (filters: OrderFilters = {}) =>
  getOrders('sellers', filters);

export const getSecondaryMarketOrders = (filters: OrderFilters = {}) =>
  getOrders('secondary-market', filters);

export const makePurchaseAsBuyer = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsBuyer);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};

export const makePurchaseAsSeller = async (
  orderId: string,
): Promise<MakePurchaseResponse> => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makePurchaseAsSeller);

    const currentUserId = getCurrentUserId();

    if (!currentUserId) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUserId,
      orderId,
    });

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};

export const makeSecondaryMarketPurchase = async (orderId: string) => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, AppCloudFunctions.makeSecondaryMarketPurchase);

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    return result.data;
  } catch (error) {
    console.error('Error making secondary market purchase:', error);
    throw error;
  }
};
