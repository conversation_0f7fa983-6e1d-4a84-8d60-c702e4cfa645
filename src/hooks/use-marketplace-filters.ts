import { useState } from 'react';

import type { SortType } from '@/components/shared/marketplace-filters';

interface UseMarketplaceFiltersReturn {
  minPrice: string;
  maxPrice: string;
  selectedCollection: string;
  sortBy: SortType;
  setMinPrice: (value: string) => void;
  setMaxPrice: (value: string) => void;
  setSelectedCollection: (value: string) => void;
  setSortBy: (value: SortType) => void;
  getFilters: () => {
    minPrice?: number;
    maxPrice?: number;
    collectionId?: string;
    sortBy: SortType;
  };
  resetFilters: () => void;
}

export const useMarketplaceFilters = (): UseMarketplaceFiltersReturn => {
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<SortType>('date_desc');

  const getFilters = () => ({
    minPrice: minPrice ? parseFloat(minPrice) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
    collectionId: selectedCollection !== 'all' ? selectedCollection : undefined,
    sortBy,
  });

  const resetFilters = () => {
    setMinPrice('');
    setMaxPrice('');
    setSelectedCollection('all');
    setSortBy('date_desc');
  };

  return {
    minPrice,
    maxPrice,
    selectedCollection,
    sortBy,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
    getFilters,
    resetFilters,
  };
};
