import { Telegraf } from "telegraf";
import {
  handleContactSupportButton,
  handleGetMyBuyOrdersButton,
  handleGetMySellOrdersButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
  handleReceiveGiftCallback,
  handleViewBuyOrdersCallback,
  handleViewSellOrdersCallback,
} from "./handlers/callbacks";
import {
  handleHelpCommand,
  handleStartCommand,
  handleHealthCommand,
} from "./handlers/commands";
import { businessConnectionMiddleware } from "./middleware/business-connection";

import { loadEnvironment } from "./config/env-loader";

loadEnvironment();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables list");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(businessConnectionMiddleware);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);
bot.command("health", handleHealthCommand);

bot.hears("🛒 My Buy Orders", handleGetMyBuyOrdersButton);
bot.hears("💰 My Sell Orders", handleGetMySellOrdersButton);
bot.hears("📞 Contact Support", handleContactSupportButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action("view_buy_orders", handleViewBuyOrdersCallback);
bot.action("view_sell_orders", handleViewSellOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action(/^receive_gift_(.+)$/, handleReceiveGiftCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx?.reply?.("Sorry, something went wrong. Please try again later.");
});

process.once("SIGINT", () => {
  console.log("Received SIGINT, stopping bot...");
  bot.stop("SIGINT");
});

process.once("SIGTERM", () => {
  console.log("Received SIGTERM, stopping bot...");
  bot.stop("SIGTERM");
});

export default bot;
