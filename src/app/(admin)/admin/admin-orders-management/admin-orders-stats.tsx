import type { LucideIcon } from 'lucide-react';
import {
  <PERSON><PERSON>hart<PERSON>,
  <PERSON><PERSON>ircle,
  DollarSign,
  Gift,
  RefreshCw,
  ShoppingCart,
  XCircle,
} from 'lucide-react';

import type { AdminOrderStats } from '@/api/admin-api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StatCardProps {
  title: string;
  value: number;
  icon: LucideIcon;
  description: string;
}

function StatCard({ title, value, icon: Icon, description }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

interface AdminOrdersStatsProps {
  stats: AdminOrderStats | null;
  loading: boolean;
}

export function AdminOrdersStats({ stats, loading }: AdminOrdersStatsProps) {
  if (loading || !stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  const totalNonAdminOrders =
    stats.nonAdminOrders.active +
    stats.nonAdminOrders.paid +
    stats.nonAdminOrders.giftSentToRelayer +
    stats.nonAdminOrders.cancelled +
    stats.nonAdminOrders.fulfilled;

  const totalAdminOrders =
    stats.adminOrders.active +
    stats.adminOrders.paid +
    stats.adminOrders.giftSentToRelayer +
    stats.adminOrders.cancelled +
    stats.adminOrders.fulfilled;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Total Orders"
          value={stats.totalOrders}
          icon={BarChart3}
          description="All orders in system"
        />
        <StatCard
          title="Non-Admin Orders"
          value={totalNonAdminOrders}
          icon={ShoppingCart}
          description="Orders without admin involvement"
        />
        <StatCard
          title="Admin Orders"
          value={totalAdminOrders}
          icon={DollarSign}
          description="Orders with admin involvement"
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <StatCard
          title="Active (Non-Admin)"
          value={stats.nonAdminOrders.active}
          icon={RefreshCw}
          description="Active orders without admins"
        />
        <StatCard
          title="Paid (Non-Admin)"
          value={stats.nonAdminOrders.paid}
          icon={DollarSign}
          description="Paid orders without admins"
        />
        <StatCard
          title="Gift Sent (Non-Admin)"
          value={stats.nonAdminOrders.giftSentToRelayer}
          icon={Gift}
          description="Gift sent without admins"
        />
        <StatCard
          title="Cancelled (Non-Admin)"
          value={stats.nonAdminOrders.cancelled}
          icon={XCircle}
          description="Cancelled orders without admins"
        />
        <StatCard
          title="Fulfilled (Non-Admin)"
          value={stats.nonAdminOrders.fulfilled}
          icon={CheckCircle}
          description="Fulfilled orders without admins"
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <StatCard
          title="Active (Admin)"
          value={stats.adminOrders.active}
          icon={RefreshCw}
          description="Active orders with admins"
        />
        <StatCard
          title="Paid (Admin)"
          value={stats.adminOrders.paid}
          icon={DollarSign}
          description="Paid orders with admins"
        />
        <StatCard
          title="Gift Sent (Admin)"
          value={stats.adminOrders.giftSentToRelayer}
          icon={Gift}
          description="Gift sent with admins"
        />
        <StatCard
          title="Cancelled (Admin)"
          value={stats.adminOrders.cancelled}
          icon={XCircle}
          description="Cancelled orders with admins"
        />
        <StatCard
          title="Fulfilled (Admin)"
          value={stats.adminOrders.fulfilled}
          icon={CheckCircle}
          description="Fulfilled orders with admins"
        />
      </div>
    </div>
  );
}
