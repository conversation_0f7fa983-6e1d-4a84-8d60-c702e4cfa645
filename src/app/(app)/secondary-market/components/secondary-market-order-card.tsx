'use client';

import { BaseOrderCard } from '@/components/shared/base-order-card';
import { PriceButton, PriceRow } from '@/components/shared/price-display';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';

interface SecondaryMarketOrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
  animated?: boolean;
}

export function SecondaryMarketOrderCard({
  animated,
  order,
  collection,
  onClick,
}: SecondaryMarketOrderCardProps) {
  return (
    <BaseOrderCard
      animated={animated}
      order={order}
      collection={collection}
      onClick={onClick}
    >
      <div className="space-y-1 mb-2">
        <PriceRow
          label="Primary:"
          amount={order.price}
          className="text-[#708499]"
        />
        <PriceRow
          label="Secondary:"
          amount={order.secondaryMarketPrice || 0}
          className="text-[#6ab2f2]"
          tonLogoClassName="text-[#6ab2f2]"
        />
      </div>

      <PriceButton amount={order.secondaryMarketPrice || 0} />
    </BaseOrderCard>
  );
}
