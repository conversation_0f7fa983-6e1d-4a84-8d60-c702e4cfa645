'use client';

import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { Button } from '@/components/ui/button';
import { CachePatterns, type OrderEntity } from '@/constants/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useMarketplaceFilters } from '@/hooks/use-marketplace-filters';
import { useRootContext } from '@/root-context';

import { ResellOrderDrawer } from './components/resell-order-drawer';
import { SecondaryOrderDetailsDrawer } from './components/secondary-order-details-drawer';
import { useSecondaryMarketOrders } from './hooks/use-secondary-market-orders';

export default function SecondaryMarketPage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showResellDrawer, setShowResellDrawer] = useState(false);

  const filters = useMarketplaceFilters();
  const ordersFilters = {
    ...filters.getFilters(),
    currentUserId: currentUser?.id,
  };

  const { state, loadOrders, loadMoreOrders } = useSecondaryMarketOrders({
    filters: ordersFilters,
  });

  const loadMoreRef = useInfiniteScroll({
    hasMore: state.hasMore,
    loading: state.loading || state.loadingMore,
    onLoadMore: loadMoreOrders,
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filters.minPrice,
    filters.maxPrice,
    filters.selectedCollection,
    filters.sortBy,
    currentUser,
  ]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders(true);
    refetchUser();
  };

  const handleResellClick = () => {
    setShowResellDrawer(true);
  };

  const handleOrderResold = () => {
    handleOrderAction();
  };

  return (
    <div className="relative space-y-2 bg-[#17212b] min-h-screen">
      <Button
        onClick={handleResellClick}
        className="fixed! rounded-[50%]! bottom-16 right-2 w-[42px]! h-[42px]! p-0! flex justify-center"
      >
        <Plus />
      </Button>
      <MarketplaceFilters
        minPrice={filters.minPrice}
        maxPrice={filters.maxPrice}
        selectedCollection={filters.selectedCollection}
        sortBy={filters.sortBy}
        collections={collections}
        onMinPriceChange={filters.setMinPrice}
        onMaxPriceChange={filters.setMaxPrice}
        onCollectionChange={filters.setSelectedCollection}
        onSortChange={filters.setSortBy}
      />

      <MarketplaceOrderList
        variant="secondary-market"
        orders={state.orders}
        collections={collections}
        loading={state.loading}
        loadingMore={state.loadingMore}
        emptyMessage="No orders found in secondary market"
        onOrderClick={handleOrderClick}
        ref={loadMoreRef}
      />

      <SecondaryOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        onOrderAction={handleOrderAction}
      />

      <ResellOrderDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        collections={collections}
        onOrderResold={handleOrderResold}
      />
    </div>
  );
}
