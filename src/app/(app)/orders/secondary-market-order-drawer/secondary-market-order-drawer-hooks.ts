import { useCallback, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { getAppConfig } from '@/api/app-config-api';
import { setSecondaryMarketPrice } from '@/api/order-api';
import type { OrderEntity } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

export function useSecondaryMarketValidation() {
  const [minPrice, setMinPrice] = useState<number>(1);

  const loadMinPrice = useCallback(async () => {
    try {
      const feesConfig = await getAppConfig();
      if (feesConfig?.min_secondary_market_price) {
        setMinPrice(feesConfig.min_secondary_market_price);
      }
    } catch (error) {
      console.error('Error loading min secondary market price:', error);
    }
  }, []);

  const validatePrice = useCallback(
    (price: string): boolean => {
      const priceValue = parseFloat(price);
      return !isNaN(priceValue) && priceValue >= minPrice;
    },
    [minPrice],
  );

  return { minPrice, loadMinPrice, validatePrice };
}

export function useSecondaryMarketTransaction() {
  const { currentUser } = useRootContext();
  const [loading, setLoading] = useState(false);

  const executeTransaction = useCallback(
    async (order: OrderEntity, price: number): Promise<boolean> => {
      if (!order || !currentUser) return false;

      setLoading(true);
      try {
        const result = await setSecondaryMarketPrice(order.id!, price);

        if (result.success) {
          const isUpdating =
            order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
          toast.success(
            isUpdating
              ? 'Secondary market order updated successfully!'
              : 'Secondary market order created successfully!',
          );
          return true;
        } else {
          toast.error(
            result.message || 'Failed to create secondary market order',
          );
          return false;
        }
      } catch (error: unknown) {
        console.error('Error creating secondary market order:', error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'Failed to create secondary market order';
        toast.error(errorMessage);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [currentUser],
  );

  return { executeTransaction, loading };
}

export function useSecondaryMarketState(
  order: OrderEntity | null,
  isOpen: boolean,
) {
  const [price, setPrice] = useState('');
  const { loadMinPrice } = useSecondaryMarketValidation();

  useEffect(() => {
    if (isOpen) {
      loadMinPrice();
      if (order?.secondaryMarketPrice && order.secondaryMarketPrice > 0) {
        setPrice(order.secondaryMarketPrice.toString());
      } else {
        setPrice('');
      }
    }
  }, [isOpen, order?.secondaryMarketPrice, loadMinPrice]);

  const resetForm = useCallback(() => {
    setPrice('');
  }, []);

  return { price, setPrice, resetForm };
}
