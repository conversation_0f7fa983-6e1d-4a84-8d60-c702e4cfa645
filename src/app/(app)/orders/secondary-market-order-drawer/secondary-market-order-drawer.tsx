'use client';

import { TrendingUp } from 'lucide-react';

import { BaseDrawer } from '@/components/ui/drawer/base-drawer';
import { DrawerActions } from '@/components/ui/drawer/drawer-actions';
import { DrawerHeader } from '@/components/ui/drawer/drawer-header';
import type { OrderEntity } from '@/constants/core.constants';

import {
  useSecondaryMarketState,
  useSecondaryMarketTransaction,
  useSecondaryMarketValidation,
} from './secondary-market-order-drawer-hooks';
import { SecondaryMarketOrderDrawerOrderInfo } from './secondary-market-order-drawer-order-info';
import { SecondaryMarketOrderDrawerPriceInput } from './secondary-market-order-drawer-price-input';
import { SecondaryMarketOrderDrawerWarning } from './secondary-market-order-drawer-warning';

interface SecondaryMarketOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderCreated: (updatedPrice?: number) => void;
}

export function SecondaryMarketOrderDrawer({
  open,
  onOpenChange,
  order,
  onOrderCreated,
}: SecondaryMarketOrderDrawerProps) {
  const { price, setPrice } = useSecondaryMarketState(order, open);
  const { minPrice, validatePrice } = useSecondaryMarketValidation();
  const { executeTransaction, loading } = useSecondaryMarketTransaction();

  const priceValue = parseFloat(price);
  const isValidPrice = validatePrice(price);
  const isUpdating =
    order?.secondaryMarketPrice && order.secondaryMarketPrice > 0;

  const handleCreateOrder = async () => {
    if (!order || !isValidPrice) return;

    const success = await executeTransaction(order, priceValue);
    if (success) {
      onOrderCreated(priceValue);
      onOpenChange(false);
    }
  };

  if (!order) return null;

  return (
    <BaseDrawer
      open={open}
      onOpenChange={onOpenChange}
      zIndex={60}
      height="h-[70vh]"
      className="mt-24"
      shouldScaleBackground={false}
      modal={false}
    >
      <DrawerHeader
        icon={TrendingUp}
        title={isUpdating ? 'Update Resale Order' : 'Create Resale Order'}
        subtitle={
          isUpdating
            ? 'Update your price for reselling this order on the secondary market'
            : 'Set your price for reselling this order on the secondary market'
        }
      />

      <SecondaryMarketOrderDrawerOrderInfo order={order} />

      <div className="space-y-4">
        <SecondaryMarketOrderDrawerPriceInput
          value={price}
          onChange={setPrice}
          minPrice={minPrice}
          isValid={isValidPrice}
        />

        <SecondaryMarketOrderDrawerWarning />
      </div>

      <DrawerActions
        onPrimary={handleCreateOrder}
        onCancel={() => onOpenChange(false)}
        primaryLabel={isUpdating ? 'Update Order' : 'Create Order'}
        primaryLoadingLabel={isUpdating ? 'Updating...' : 'Creating...'}
        primaryDisabled={!isValidPrice}
        loading={loading}
      />
    </BaseDrawer>
  );
}
