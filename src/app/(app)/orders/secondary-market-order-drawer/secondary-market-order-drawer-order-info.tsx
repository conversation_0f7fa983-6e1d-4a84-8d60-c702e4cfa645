import type { OrderEntity } from '@/constants/core.constants';

interface SecondaryMarketOrderDrawerOrderInfoProps {
  order: OrderEntity;
}

export function SecondaryMarketOrderDrawerOrderInfo({
  order,
}: SecondaryMarketOrderDrawerOrderInfoProps) {
  return (
    <div className="bg-[#232e3c] rounded-2xl p-4 mb-6">
      <div className="flex justify-between items-center mb-2">
        <span className="text-[#708499] text-sm">Original Order Price</span>
        <span className="text-[#f5f5f5] font-semibold">{order.price} TON</span>
      </div>
      <div className="flex justify-between items-center">
        <span className="text-[#708499] text-sm">Order Number</span>
        <span className="text-[#6ab2f2] font-semibold">
          #{order.number || order.id?.slice(-6)}
        </span>
      </div>
    </div>
  );
}
