import { User } from 'lucide-react';

import { OrderStatusIcon } from '@/components/ui/order/order-status-icon';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { formatOrderStatus } from '@/utils/order-utils';

interface UserOrderCardHeaderProps {
  order: OrderEntity;
  userType: UserType;
}

export function UserOrderCardHeader({
  order,
  userType,
}: UserOrderCardHeaderProps) {
  return (
    <div className="flex w-full items-center justify-between mb-1 absolute top-0 left-0 px-2 pt-1.5">
      <div className="flex items-center gap-1">
        <OrderStatusIcon status={order.status} className="w-4 h-4" />
        <span className="text-[10px] text-[#708499]">
          {formatOrderStatus(order.status)}
        </span>
      </div>
      <div className="flex items-center gap-1">
        <User className="w-2.5 h-2.5 text-[#6ab2f2]" />
        <span className="text-[10px] text-[#6ab2f2] capitalize">
          {userType}
        </span>
      </div>
    </div>
  );
}
