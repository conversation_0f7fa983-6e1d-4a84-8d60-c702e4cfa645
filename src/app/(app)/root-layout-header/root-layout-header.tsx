'use client';

import { <PERSON><PERSON>, Button as TgButton } from '@telegram-apps/telegram-ui';
import { Minus, Plus, User } from 'lucide-react';

import { TonLogo } from '@/components/TonLogo';
import { WithdrawDrawer } from '@/components/WithdrawDrawer';
import { cn } from '@/lib/utils';

import { DepositDrawer } from '../../../components/deposit-drawer';
import {
  useHeaderState,
  useWalletConnection,
} from './root-layout-header-hooks';
import { WalletSection } from './root-layout-header-wallet-section';

const BUTTON_CLASSES = {
  actionButton: 'min-w-6! h-6! p-0! rounded-full!',
};

export default function RootLayoutHeader() {
  const {
    showWalletDropdown,
    setShowWalletDropdown,
    showDepositDrawer,
    setShowDepositDrawer,
    showWithdrawDrawer,
    setShowWithdrawDrawer,
    dropdownRef,
    balanceInfo,
    onProfileButtonClick,
    currentUser,
  } = useHeaderState();

  const {
    tonWalletAddress,
    isConnecting,
    isAuthenticating,
    isLoading,
    handleWalletAction,
    handleDisconnectWallet,
    formatAddress,
  } = useWalletConnection();

  const handleDepositClick = () => {
    setShowDepositDrawer(true);
  };

  const handleWithdrawClick = () => {
    setShowWithdrawDrawer(true);
  };

  return (
    <header className="fixed top-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] p-2 border-b border-[#3a4a5c] z-50">
      <div className="flex items-center justify-between gap-1 min-w-0">
        <div className="flex items-center gap-1 bg-[#232e3c]/60 min-w-0 p-2 rounded-lg">
          <div className="flex items-center gap-1 min-w-0 flex-shrink">
            <TonLogo size={20} className="w-5 h-5 flex-shrink-0" />
            <div className="text-sm font-bold truncate">
              {balanceInfo.available}
            </div>
          </div>

          <div className="flex items-center gap-1 ml-1">
            <TgButton
              className={BUTTON_CLASSES.actionButton}
              onClick={handleDepositClick}
            >
              <Plus className="w-4 h-4 stroke-[2.5]" />
            </TgButton>
            <TgButton
              className={BUTTON_CLASSES.actionButton}
              onClick={handleWithdrawClick}
            >
              <Minus className="w-4 h-4 stroke-[2.5]" />
            </TgButton>
          </div>

          <div
            onClick={onProfileButtonClick}
            className={cn(
              'w-10 h-10 cursor-pointer rounded-full overflow-hidden bg-[#232e3c] flex items-center justify-center',
              isLoading && 'pointer-events-none',
            )}
          >
            {currentUser?.photoURL ? (
              <Avatar size={40} src={currentUser.photoURL} />
            ) : (
              <User className="w-4 h-4 text-[#v08499]" />
            )}
          </div>
        </div>

        <WalletSection
          {...{
            tonWalletAddress,
            isConnecting,
            isAuthenticating,
            showWalletDropdown,
            setShowWalletDropdown,
            dropdownRef,
            formatAddress,
          }}
          onWalletAction={handleWalletAction}
          onDisconnectWallet={handleDisconnectWallet}
        />
      </div>

      <DepositDrawer
        open={showDepositDrawer}
        onOpenChange={setShowDepositDrawer}
      />

      <WithdrawDrawer
        open={showWithdrawDrawer}
        onOpenChange={setShowWithdrawDrawer}
      />
    </header>
  );
}
