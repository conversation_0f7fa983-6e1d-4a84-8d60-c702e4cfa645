import { Address } from '@ton/core';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';

import { updateUserWallet } from '@/api/user-api';
import { useTelegramAuth } from '@/hooks/use-telegram-auth';
import { useRootContext } from '@/root-context';

export function useWalletConnection() {
  const [tonConnectUI] = useTonConnectUI();
  const [tonWalletAddress, setTonWalletAddress] = useState<string>('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { currentUser, setCurrentUser, refetchUser } = useRootContext();

  const { authenticate, isLoading } = useTelegramAuth({
    onSuccess: async () => {
      toast.success('Authenticated successfully');

      if (isAuthenticating) {
        setIsAuthenticating(false);
        setIsConnecting(true);
        try {
          await refetchUser();
          await tonConnectUI.openModal();
        } catch (error) {
          console.error('Error opening wallet modal after auth:', error);
          setIsConnecting(false);
        }
      }
    },
    onError: (error) => {
      toast.error(`Authentication failed: ${error}`);
      setIsAuthenticating(false);
    },
  });

  useEffect(() => {
    if (tonConnectUI.account?.address) {
      setTonWalletAddress(tonConnectUI.account.address);
    } else {
      setTonWalletAddress('');
    }
  }, [tonConnectUI.account?.address]);

  const handleWalletConnection = useCallback(
    async (address: string) => {
      try {
        setTonWalletAddress(address);

        if (currentUser?.id) {
          try {
            const parsedAddress = Address.parse(address);
            const userFriendlyAddress = parsedAddress.toString();
            const updatedUser = await updateUserWallet(
              currentUser.id,
              userFriendlyAddress,
            );
            setCurrentUser(updatedUser);
            toast.success('Wallet connected and saved to profile');
          } catch (error) {
            console.error('Error saving wallet to profile:', error);
            toast.error(
              `Wallet connected but failed to save to profile: ${error instanceof Error ? error.message : 'Unknown error'}`,
            );
          }
        }
      } catch (error) {
        console.error('Error connecting wallet:', error);
        toast.error('Failed to connect wallet');
      } finally {
        setIsConnecting(false);
      }
    },
    [currentUser?.id, setCurrentUser],
  );

  const handleWalletDisconnection = useCallback(async () => {
    try {
      setTonWalletAddress('');
      toast.success('Wallet disconnected');
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  }, []);

  const handleWalletAction = async () => {
    if (!currentUser) {
      setIsAuthenticating(true);
      try {
        await authenticate();
      } catch (error) {
        console.error('Authentication failed:', error);
        setIsAuthenticating(false);
        return;
      }
      setIsAuthenticating(false);
    }

    if (tonConnectUI.account?.address) {
      return 'show-dropdown';
    } else {
      setIsConnecting(true);
      try {
        await tonConnectUI.openModal();
      } catch (error) {
        console.error('Error opening wallet modal:', error);
        setIsConnecting(false);
      }
    }
  };

  const handleDisconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
      setTonWalletAddress('');
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = tonConnectUI.onStatusChange(async (wallet) => {
      if (wallet && isConnecting) {
        await handleWalletConnection(wallet.account.address);
      } else if (!wallet && !isConnecting) {
        await handleWalletDisconnection();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [
    tonConnectUI,
    handleWalletConnection,
    handleWalletDisconnection,
    isConnecting,
  ]);

  useEffect(() => {
    const handleModalStateChange = () => {
      if (
        !tonConnectUI.modal.state?.status ||
        tonConnectUI.modal.state.status === 'closed'
      ) {
        setIsConnecting(false);
      }
    };

    const unsubscribe = tonConnectUI.modal.onStateChange(
      handleModalStateChange,
    );
    return () => {
      unsubscribe();
    };
  }, [tonConnectUI]);

  const formatAddress = (address: string) => {
    if (!address) return '';
    try {
      const addr = Address.parse(address);
      const formatted = addr.toString();
      return `${formatted.slice(0, 3)}...${formatted.slice(-3)}`;
    } catch {
      return `${address.slice(0, 3)}...${address.slice(-3)}`;
    }
  };

  return {
    tonWalletAddress,
    isConnecting,
    isAuthenticating,
    isLoading,
    handleWalletAction,
    handleDisconnectWallet,
    formatAddress,
  };
}

export function useHeaderState() {
  const router = useRouter();
  const [showWalletDropdown, setShowWalletDropdown] = useState(false);
  const [showDepositDrawer, setShowDepositDrawer] = useState(false);
  const [showWithdrawDrawer, setShowWithdrawDrawer] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { currentUser, refetchUser } = useRootContext();
  const { authenticate } = useTelegramAuth({
    onSuccess: async () => {
      try {
        await refetchUser();
      } catch (error) {
        console.error('Error refetching user after auth:', error);
      }
      toast.success('Authenticated successfully');
    },
    onError: (error) => {
      toast.error(`Authentication failed: ${error}`);
    },
  });

  const balanceInfo = useMemo(() => {
    if (!currentUser?.balance) {
      return { available: '0.00', locked: 0, hasLocked: false };
    }

    const available = (
      currentUser.balance.sum - currentUser.balance.locked
    ).toFixed(2);
    const locked = currentUser.balance.locked;
    const hasLocked = locked > 0;

    return { available, locked, hasLocked };
  }, [currentUser?.balance]);

  const onProfileButtonClick = async () => {
    if (currentUser) {
      router.push('/profile');
      return;
    }

    await authenticate();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowWalletDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return {
    showWalletDropdown,
    setShowWalletDropdown,
    showDepositDrawer,
    setShowDepositDrawer,
    showWithdrawDrawer,
    setShowWithdrawDrawer,
    dropdownRef,
    balanceInfo,
    onProfileButtonClick,
    currentUser,
  };
}
