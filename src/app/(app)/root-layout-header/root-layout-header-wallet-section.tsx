import { Button as TgButton } from '@telegram-apps/telegram-ui';
import { ChevronDown, LogOut } from 'lucide-react';

import { TonLogo } from '@/components/TonLogo';
import { cn } from '@/lib/utils';

interface WalletSectionProps {
  tonWalletAddress: string;
  isConnecting: boolean;
  isAuthenticating: boolean;
  showWalletDropdown: boolean;
  setShowWalletDropdown: (show: boolean) => void;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
  onWalletAction: () => Promise<string | void>;
  onDisconnectWallet: () => Promise<void>;
  formatAddress: (address: string) => string;
}

export function WalletSection({
  tonWalletAddress,
  isConnecting,
  isAuthenticating,
  showWalletDropdown,
  setShowWalletDropdown,
  dropdownRef,
  onWalletAction,
  onDisconnectWallet,
  formatAddress,
}: WalletSectionProps) {
  const handleWalletClick = async () => {
    const result = await onWalletAction();
    if (result === 'show-dropdown') {
      setShowWalletDropdown(!showWalletDropdown);
    }
  };

  const handleDisconnect = async () => {
    await onDisconnectWallet();
    setShowWalletDropdown(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <TgButton
        className={cn(
          '[&>h6]:flex [&>h6]:items-center [&>h6]:gap-1 bg-[#27A3E9]!',
          'max-w-[110px] xs:max-w-fit',
        )}
        onClick={handleWalletClick}
        disabled={isConnecting || isAuthenticating}
      >
        <TonLogo size={12} className="w-6 h-6" />
        <span className="truncate -ml-1">
          {isAuthenticating
            ? 'Authenticating...'
            : isConnecting
              ? 'Connecting...'
              : tonWalletAddress
                ? formatAddress(tonWalletAddress)
                : 'Connect'}
        </span>
        {tonWalletAddress && <ChevronDown className="w-3 h-3 ml-1" />}
      </TgButton>

      {showWalletDropdown && tonWalletAddress && (
        <div className="absolute right-0 top-full mt-1 bg-[#232e3c] border border-[#3a4a5c] rounded-lg shadow-lg py-1 min-w-[120px] z-50">
          <button
            onClick={handleDisconnect}
            className="w-full px-3 py-2 text-left text-sm text-[#f5f5f5] hover:bg-[#6ab2f2] hover:text-white flex items-center gap-2"
          >
            <LogOut className="w-3 h-3" />
            Disconnect
          </button>
        </div>
      )}
    </div>
  );
}
