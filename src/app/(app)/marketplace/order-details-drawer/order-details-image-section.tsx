import { TgsOrImage } from '@/components/TgsOrImage';
import type { CollectionEntity } from '@/constants/core.constants';

interface OrderDetailsImageSectionProps {
  collectionId: string;
  collection: CollectionEntity | null;
}

export function OrderDetailsImageSection({
  collectionId,
  collection,
}: OrderDetailsImageSectionProps) {
  return (
    <div className="relative">
      <div className="aspect-square relative rounded-2xl overflow-hidden bg-gradient-to-br from-[#232e3c] to-[#1a252f] p-8 border border-[#3a4a5c]/50">
        <TgsOrImage
          isImage={false}
          collectionId={collectionId}
          imageProps={{
            alt: collection?.name || 'Order item',
            fill: true,
            className: 'object-contain drop-shadow-2xl',
          }}
          tgsProps={{
            style: { height: '100%', width: '100%' },
          }}
        />
      </div>
    </div>
  );
}
