'use client';

import type { ReactNode } from 'react';
import { Drawer } from 'vaul';

interface BaseDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: ReactNode;
  className?: string;
  contentClassName?: string;
  zIndex?: number;
  height?: string;
  shouldScaleBackground?: boolean;
  modal?: boolean;
  dismissible?: boolean;
}

export function BaseDrawer({
  open,
  onOpenChange,
  children,
  className = '',
  contentClassName = '',
  zIndex = 100,
  height = 'max-h-[85vh]',
  shouldScaleBackground = true,
  modal = true,
  dismissible = true,
}: BaseDrawerProps) {
  return (
    <Drawer.Root
      {...{
        shouldScaleBackground,
        modal,
        dismissible,
        onOpenChange,
        open,
      }}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className={`fixed inset-0 bg-black/40 z-[${zIndex}]`} />
        <Drawer.Content
          className={`bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[${zIndex + 1}] outline-none focus:outline-none ${className}`}
        >
          <div
            className={`p-6 bg-[#17212b] rounded-t-[20px] flex-1 ${height} overflow-y-auto ${contentClassName}`}
          >
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />
            <div className="max-w-md mx-auto space-y-6">{children}</div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
