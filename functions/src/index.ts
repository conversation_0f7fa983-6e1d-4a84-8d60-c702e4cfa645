import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { beforeUserCreated } from "firebase-functions/v2/identity";

import { UserEntity } from "./types";
import { monitorTonTransactions } from "./ton-monitor";
import { getConfig } from "./config";

if (!admin.apps.length) {
  const config = getConfig();

  // For Firebase Functions, we need to explicitly set the service account
  // to ensure it has the necessary permissions for custom token creation
  const serviceAccount = config.firebase?.service_account_key;

  if (serviceAccount) {
    // Use service account key if provided
    admin.initializeApp({
      credential: admin.credential.cert(JSON.parse(serviceAccount)),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with service account key");
  } else {
    // Use default credentials with explicit project ID
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: config.app.project_id,
    });
    console.log("Firebase initialized with application default credentials");
  }
}

admin.firestore().settings({
  ignoreUndefinedProperties: true,
});

const db = admin.firestore();

export const createUserRecord = beforeUserCreated(async (event) => {
  const user = event.data;

  if (!user) {
    console.error("No user data provided in auth event");
    return;
  }

  const userRecord: UserEntity = {
    id: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    role: "user",
    balance: { sum: 0, locked: 0 },
  };

  try {
    await db.collection("users").doc(user.uid).set(userRecord);
    console.log(`User record created for ${user.uid}`);
  } catch (error) {
    console.error("Error creating user record:", error);
    throw new functions.https.HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating user record."
    );
  }
});

export const tonTransactionMonitor = onSchedule(
  {
    schedule: "* * * * *",
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "TON transaction monitor triggered at:",
        new Date().toISOString()
      );
      await monitorTonTransactions();
      console.log("TON transaction monitor completed successfully");
    } catch (error) {
      console.error("TON transaction monitor failed:", error);
    }
  }
);

export { expiredOrdersMonitor } from "./expired-orders-monitor";

export { limitedCollectionsMonitor } from "./limited-collections-monitor";

export {
  getOrderByIdByBot,
  getUserOrdersByBot,
  sendGiftToRelayerByBot,
  completePurchaseByBot,
} from "./order-functions/bot-order-functions";

export {
  createOrderAsBuyer,
  makePurchaseAsBuyer,
} from "./order-functions/buyer-order-functions";

export {
  createOrderAsSeller,
  makePurchaseAsSeller,
} from "./order-functions/seller-order-functions";

export { cancelUserOrder } from "./order-functions/general-order-functions";

export { withdrawFunds } from "./withdraw-functions";

export { withdrawRevenue } from "./revenue-functions";

export { signInWithTelegram } from "./telegram-auth-functions";

export { changeUserData } from "./user-profile-functions";

export {
  setSecondaryMarketPrice,
  makeSecondaryMarketPurchase,
} from "./order-functions/secondary-market-functions";
