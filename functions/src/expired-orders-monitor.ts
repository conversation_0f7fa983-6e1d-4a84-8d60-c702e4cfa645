import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { OrderEntity } from "./types";
import { processOrderCancellation } from "./services/order-cancellation-service";

const db = admin.firestore();

/**
 * Process expired orders - applies same logic as seller rejection (case 2)
 * This function handles orders where:
 * - deadline < current date
 * - status = "paid"
 * - has both buyerId and sellerId
 */
export async function processExpiredOrders() {
  try {
    console.log("Starting expired orders processing...");

    const now = admin.firestore.Timestamp.now();

    // Query for expired orders
    const expiredOrdersQuery = db
      .collection("orders")
      .where("status", "==", "paid")
      .where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      console.log("No expired orders found");
      return;
    }

    console.log(
      `Found ${expiredOrdersSnapshot.size} expired orders to process`
    );

    // Process each expired order
    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        console.log(`Skipping order ${order.id}: missing buyer or seller`);
        continue;
      }

      try {
        // Use unified cancellation logic - expired orders are treated as seller cancellations
        const result = await processOrderCancellation(order, order.sellerId);
        console.log(
          `Successfully processed expired order ${order.id}: ${result.message}`
        );
      } catch (error) {
        console.error(`Failed to process expired order ${order.id}:`, error);
        // Continue processing other orders even if one fails
      }
    }

    console.log("Expired orders processing completed");
  } catch (error) {
    console.error("Error in processExpiredOrders:", error);
    throw error;
  }
}

export const expiredOrdersMonitor = onSchedule(
  {
    schedule: "0 0 * * *", // Run daily at midnight UTC
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "Expired orders monitor triggered at:",
        new Date().toISOString()
      );
      await processExpiredOrders();
      console.log("Expired orders monitor completed successfully");
    } catch (error) {
      console.error("Expired orders monitor failed:", error);
    }
  }
);
