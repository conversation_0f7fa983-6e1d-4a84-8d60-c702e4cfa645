import { HttpsError, CallableRequest } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { UserEntity, UserType } from "../types";

export interface AuthenticatedRequest extends CallableRequest {
  auth: NonNullable<CallableRequest["auth"]>;
}

export interface ValidatedUserRequest extends AuthenticatedRequest {
  user: UserEntity;
}

export function requireAuthentication(request: CallableRequest) {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }
  return request as AuthenticatedRequest;
}

export function requireUserPermission(
  request: AuthenticatedRequest,
  userId: string,
  operation: string = "operation"
) {
  if (request.auth.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      `You can only perform ${operation} for yourself.`
    );
  }
}

export function validateRequiredParams(data: any, requiredParams: string[]) {
  for (const param of requiredParams) {
    if (!data[param]) {
      throw new HttpsError("invalid-argument", `${param} is required.`);
    }
  }
}

export function validatePositiveAmount(
  amount: number,
  fieldName: string = "amount"
) {
  if (!amount || amount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      `${fieldName} must be greater than 0.`
    );
  }
}

export async function getUserData(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection("users").doc(userId).get();

  if (!userDoc.exists) {
    throw new HttpsError("not-found", "User not found.");
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}

export async function requireAdminRole(userId: string) {
  const user = await getUserData(userId);

  if (user.role !== "admin") {
    throw new HttpsError(
      "permission-denied",
      "Only admin users can perform this operation."
    );
  }

  return user;
}

export function requireTonWallet(user: UserEntity) {
  if (!user.ton_wallet_address) {
    throw new HttpsError(
      "failed-precondition",
      "User does not have a TON wallet address configured."
    );
  }
}

export async function authenticateAndGetUser(request: CallableRequest) {
  const authRequest = requireAuthentication(request);
  const user = await getUserData(authRequest.auth.uid);

  return { request: authRequest, user };
}

export function validateBuyerOwnership(
  request: AuthenticatedRequest,
  buyerId: string
) {
  requireUserPermission(request, buyerId, "buyer operations");
}

export function validateSellerOwnership(
  request: AuthenticatedRequest,
  sellerId: string
) {
  requireUserPermission(request, sellerId, "seller operations");
}

export function validateOrderCreationParams(
  data: {
    sellerId?: string;
    buyerId?: string;
    collectionId: string;
    price: number;
  },
  userType: UserType
) {
  const userIdField = userType === UserType.BUYER ? "buyerId" : "sellerId";

  validateRequiredParams(data, [userIdField, "collectionId", "price"]);
  validatePositiveAmount(data.price);
}

export function validatePurchaseParams(
  data: Record<string, any>,
  userType: UserType
) {
  const userIdField = userType === UserType.BUYER ? "buyerId" : "sellerId";

  validateRequiredParams(data, [userIdField, "orderId"]);
}
