import * as admin from "firebase-admin";
import { OrderStatus } from "../types";
import { lockFunds } from "./balance-service";
import {
  validateBuyerPurchase,
  validateSellerPurchase,
} from "./order-validation-service";
import { addDeadlineIfMarketCollection } from "./deadline-service";

export async function processPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
    userType: "buyer" | "seller";
  }
): Promise<{
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}> {
  const { userId, orderId, userType } = params;

  // Validate purchase based on user type
  const validation =
    userType === "buyer"
      ? await validateBuyerPurchase(db, { userId, orderId })
      : await validateSellerPurchase(db, { userId, orderId });

  const { order, lockedAmount, lockPercentage } = validation;

  // Lock funds for the user
  await lockFunds(userId, lockedAmount);

  // Prepare update data
  const updateData: any = {
    status: OrderStatus.PAID,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Set buyer or seller ID based on user type
  if (userType === "buyer") {
    updateData.buyerId = userId;
  } else {
    updateData.sellerId = userId;
  }

  // Add deadline if collection is in MARKET status
  await addDeadlineIfMarketCollection(
    db,
    order.collectionId,
    orderId,
    updateData
  );

  // Update order
  await db.collection("orders").doc(orderId).update(updateData);

  const actionMessage =
    userType === "buyer"
      ? "Waiting for seller to send gift."
      : "You can now send the gift.";

  return {
    success: true,
    message: `Purchase successful! ${lockedAmount} TON locked (${
      lockPercentage * 100
    }% of ${order.price} TON order). ${actionMessage}`,
    lockedAmount,
    orderAmount: order.price,
    lockPercentage: lockPercentage * 100,
  };
}
