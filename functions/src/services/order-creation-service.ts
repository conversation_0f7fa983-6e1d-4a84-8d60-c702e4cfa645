import * as admin from "firebase-admin";
import { CollectionEntity, OrderEntity, OrderStatus, UserType } from "../types";
import { lockFunds } from "./balance-service";
import { getNextCounterValue } from "./counter-service";
import { validateOrderCreation } from "./order-validation-service";
import { calculateOrderDeadline } from "./deadline-service";

export async function createOrder(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    collectionId: string;
    price: number;
    owned_gift_id: string | null;
    userType: UserType;
    secondaryMarketPrice: number | null;
  }
): Promise<{
  success: boolean;
  orderId: string;
  message: string;
  lockedAmount: number;
  lockPercentage: number;
}> {
  const {
    userId,
    collectionId,
    price,
    owned_gift_id,
    userType,
    secondaryMarketPrice,
  } = params;

  const validation = await validateOrderCreation(db, {
    userId,
    collectionId,
    price,
    userType,
  });

  await lockFunds(userId, validation.lockedAmount);

  const orderNumber = await getNextCounterValue("order_number");

  // Get collection data to calculate deadline
  const collectionDoc = await db
    .collection("collections")
    .doc(collectionId)
    .get();

  const collection = collectionDoc.exists
    ? (collectionDoc.data() as CollectionEntity)
    : null;

  const deadline = calculateOrderDeadline(collection);

  const orderData: Omit<OrderEntity, "id"> = {
    number: orderNumber,
    collectionId,
    price,
    status: OrderStatus.ACTIVE,
    owned_gift_id,
    ...(deadline && { deadline }),
    secondaryMarketPrice,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
  };

  if (userType === UserType.BUYER) {
    orderData.buyerId = userId;
  } else {
    orderData.sellerId = userId;
  }

  // Create the order
  const orderRef = await db.collection("orders").add(orderData);

  return {
    success: true,
    orderId: orderRef.id,
    message: `Order created successfully with ${
      validation.lockedAmount
    } TON locked (${validation.lockPercentage * 100}% of ${price} TON order)`,
    lockedAmount: validation.lockedAmount,
    lockPercentage: validation.lockPercentage,
  };
}
